import { useState, useCallback } from 'react';
import { debounce } from 'lodash';
import { Option } from './MultiFieldInput.types';

export function useAddressAutocomplete<T extends Option = Option>(findAddresses: any) {
  const [localOptions, setLocalOptions] = useState<T[]>([]);

  const debouncedFindAddresses = useCallback(
    debounce(async (query, token) => {
      try {
        const result = await findAddresses(query, token);
        if (
          result &&
          typeof result === 'object' &&
          'hits' in result &&
          Array.isArray(result.hits)
        ) {
          const newOptions = result.hits.map((item: any) => ({
            value: item.id || item.value || '',
            label: item.suggestion || item.label || '',
          })) as T[];
          setLocalOptions(newOptions);
        }
      } catch {}
    }, 1000),
    [findAddresses]
  );

  return {
    localOptions,
    setLocalOptions,
    debouncedFindAddresses,
  };
}
