'use client';

import React, { useLayoutEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useUser, SignedIn, SignedOut, RedirectToSignIn } from '@clerk/nextjs';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { Onboarding } from '@/components/Onboarding/Onboarding';
import styles from '@/containers/OnboardingPage/OnboardingPage.module.scss';

export default function OnboardingPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const { isMobile } = useBreakpoint();
  const effectRan = useRef(false);

  const hasCompletedOnboarding = user?.unsafeMetadata?.visitedOnboarding;

  useLayoutEffect(() => {
    if (effectRan.current) return;
    if (!isLoaded || !user) return;

    effectRan.current = true;

    const handleFirstLogin = async () => {
      if (!hasCompletedOnboarding) {
        try {
          await user.update({
            unsafeMetadata: {
              ...user.unsafeMetadata,
              visitedOnboarding: true,
            },
          });
        } catch (error) {
          console.error('Error setting initial onboarding metadata:', error);
        }
      }
    };

    handleFirstLogin();
  }, [user, isLoaded, router]);

  const completeOnboarding = async (addressRequest: Promise<unknown>) => {
    try {
      await Promise.all([addressRequest]);
    } catch (error) {
      console.error('Error during onboarding completion:', error);
    }
  };

  if (!isLoaded || !user || !hasCompletedOnboarding) {
    return null;
  }

  return (
    <>
      <SignedIn>
        <div className={styles.container}>
          {!isMobile && (
            <div className={styles.header}>
              <img src="/heyAlfie.svg" alt="heyAlfie" className={styles.image} />
            </div>
          )}
          <div className={isMobile ? styles.mobileOnboarding : styles.onboarding}>
            <Onboarding onComplete={completeOnboarding} />
          </div>
        </div>
      </SignedIn>
      <SignedOut>
        <RedirectToSignIn />
      </SignedOut>
    </>
  );
}
